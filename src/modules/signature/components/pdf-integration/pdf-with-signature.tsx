import PdfViewer from "@/modules/pdf/components/pdf";
import { IPdfOverlay } from "@/modules/pdf/types/pdf-overlay.interface";
import { useAtomValue, useSetAtom } from "jotai";
import React, { useCallback, useEffect, useMemo, useRef } from "react";
import { usePdfWithOverlays } from "../../../pdf/hooks/overlay/use-pdf-with-overlays.hook";
import { useAutoSignatureScroll } from "../../hooks/pdf-integration/use-signature-scroll.hook";
import { SignatureOverlayRenderer } from "../../lib/pdf-integration/signature-overlay-renderer";
import { mainRequisitToSignAtom } from "../../states/signature/main-requisit-to-sign.state";
import { rubricSvgString } from "../../states/rubric/rubric-svg.state";
import { signaturePosition<PERSON>tom } from "../../states/signature/signature-position.state";
import { ISignatureOverlay, ISignatureRenderData } from "../../types/signature/signature-overlay.interface";

interface PdfWithSignatureProps {
	id: string;
	buffer: ArrayBuffer;
	isModal?: boolean;
	showSignatureAsPreview?: boolean;
	zoom?: number;
}

/**
 * Componente que integra PDF com funcionalidades de assinatura
 * Responsabilidade única: coordenar PDF e assinatura
 * Segue o princípio da composição
 */
const PdfWithSignature: React.FC<PdfWithSignatureProps> = ({ id, buffer, isModal, showSignatureAsPreview = true, zoom = 1 }) => {
	const containerRef = useRef<HTMLDivElement>(null);
	const signaturePosition = useAtomValue(signaturePositionAtom);
	const rubricSvg = useAtomValue(rubricSvgString);
	const setMainRequisitToSign = useSetAtom(mainRequisitToSignAtom);

	// Auto scroll para assinatura
	useAutoSignatureScroll(containerRef, zoom, signaturePosition, rubricSvg);

	// Sistema de overlays
	const { overlayManager, registerRenderer } = usePdfWithOverlays();

	// Registra o renderizador de assinatura
	useEffect(() => {
		const signatureRenderer = new SignatureOverlayRenderer();
		registerRenderer(signatureRenderer);
	}, [registerRenderer]);

	// Converte posição de assinatura para overlay
	const signatureOverlays = useMemo((): IPdfOverlay[] => {
		if (!signaturePosition || !rubricSvg) return [];

		const signatureOverlay: ISignatureOverlay = {
			id: "current-signature",
			type: "signature",
			page: signaturePosition.page,
			x: signaturePosition.x,
			y: signaturePosition.y,
			scale: signaturePosition.scale,
			svgData: rubricSvg,
			isPreview: showSignatureAsPreview,
		};

		return [signatureOverlay];
	}, [signaturePosition, rubricSvg, showSignatureAsPreview]);

	// Dados para renderização da assinatura
	const overlayData = useMemo(
		(): Record<string, ISignatureRenderData> => ({
			signature: {
				svg: rubricSvg || "",
				showPreview: showSignatureAsPreview,
				previewText: "PRÉVIA",
				previewBorderColor: "#ff7e5f",
			},
		}),
		[rubricSvg, showSignatureAsPreview]
	);

	// Handler para download
	const handleDownloadRequested = useCallback(() => {
		setMainRequisitToSign(true);
	}, [setMainRequisitToSign]);

	// Handler para clique em overlay
	const handleOverlayClick = useCallback((overlay: IPdfOverlay) => {
		if (overlay.type === "signature") {
			console.log("Assinatura clicada:", overlay);
			// Aqui pode implementar lógica adicional para clique na assinatura
		}
	}, []);

	return (
		<PdfViewer
			id={id}
			buffer={buffer}
			isModal={isModal}
			overlays={signatureOverlays}
			overlayData={overlayData}
			onOverlayClick={handleOverlayClick}
			onDownloadRequested={handleDownloadRequested}
		/>
	);
};

export default PdfWithSignature;
