import PdfViewer from "@/modules/pdf/components/pdf";
import { IPdfOverlay } from "@/modules/pdf/types/pdf-overlay.interface";
import { useAtomValue, useSetAtom } from "jotai";
import React, { useCallback, useMemo, useEffect } from "react";
import { usePdfWithOverlays } from "../../../pdf/hooks/overlay/use-pdf-with-overlays.hook";
import { useAutoSignatureScroll } from "../../hooks/pdf-integration/use-signature-scroll.hook";
import { SignatureOverlayRenderer } from "../../lib/pdf-integration/signature-overlay-renderer";
import { mainRequisitToSignAtom } from "../../states/signature/main-requisit-to-sign.state";
import { rubricSvgString } from "../../states/rubric/rubric-svg.state";
import { signaturePosition<PERSON>tom } from "../../states/signature/signature-position.state";
import { ISignatureOverlay, ISignatureRenderData } from "../../types/signature/signature-overlay.interface";

interface PdfWithSignatureProps {
	id: string;
	buffer: ArrayBuffer;
	isModal?: boolean;
	showSignatureAsPreview?: boolean;
}

/**
 * Componente que integra PDF com funcionalidades de assinatura
 * Responsabilidade única: coordenar PDF e assinatura
 * Segue o princípio da composição
 */
const PdfWithSignature: React.FC<PdfWithSignatureProps> = ({ id, buffer, isModal, showSignatureAsPreview = true }) => {
	const signaturePosition = useAtomValue(signaturePositionAtom);
	const rubricSvg = useAtomValue(rubricSvgString);
	const setMainRequisitToSign = useSetAtom(mainRequisitToSignAtom);
	useAutoSignatureScroll(1, signaturePosition, rubricSvg);

	// Sistema de overlays
	const { overlayManager } = usePdfWithOverlays();

	// Registra o renderizador de assinatura usando atoms
	useEffect(() => {
		console.log("🔧 useEffect executando - Criando renderizador de assinatura (ATOMS)");
		const renderer = new SignatureOverlayRenderer();
		console.log("🔧 Registrando renderizador via atoms:", renderer);
		overlayManager.registerRenderer(renderer);
		console.log("🔧 Renderizador registrado com sucesso via atoms");
	}, [overlayManager]); // Executa quando overlayManager estiver disponível

	// Converte posição de assinatura para overlay
	const signatureOverlays = useMemo((): IPdfOverlay[] => {
		console.log("🔍 Debug - signaturePosition:", signaturePosition);
		console.log("🔍 Debug - rubricSvg:", rubricSvg ? "SVG presente" : "SVG ausente");

		if (!signaturePosition || !rubricSvg) {
			console.log("❌ Não criando overlay - faltam dados");
			return [];
		}

		console.log("✅ Criando overlay de assinatura");
		const signatureOverlay: ISignatureOverlay = {
			id: "current-signature",
			type: "signature",
			page: signaturePosition.page,
			x: signaturePosition.x,
			y: signaturePosition.y,
			scale: signaturePosition.scale,
			svgData: rubricSvg,
			isPreview: showSignatureAsPreview,
		};

		return [signatureOverlay];
	}, [signaturePosition, rubricSvg, showSignatureAsPreview]);

	// Dados para renderização da assinatura
	const overlayData = useMemo(
		(): Record<string, ISignatureRenderData> => ({
			signature: {
				svg: rubricSvg || "",
				showPreview: showSignatureAsPreview,
				previewText: "PRÉVIA",
				previewBorderColor: "#ff7e5f",
			},
		}),
		[rubricSvg, showSignatureAsPreview]
	);

	// Handler para download
	const handleDownloadRequested = useCallback(() => {
		setMainRequisitToSign(true);
	}, [setMainRequisitToSign]);

	// Handler para clique em overlay
	const handleOverlayClick = useCallback((overlay: IPdfOverlay) => {
		if (overlay.type === "signature") {
			console.log("Assinatura clicada:", overlay);
			// Aqui pode implementar lógica adicional para clique na assinatura
		}
	}, []);

	return (
		<PdfViewer
			id={id}
			buffer={buffer}
			isModal={isModal}
			overlays={signatureOverlays}
			overlayData={overlayData}
			onOverlayClick={handleOverlayClick}
			onDownloadRequested={handleDownloadRequested}
		/>
	);
};

export default PdfWithSignature;
