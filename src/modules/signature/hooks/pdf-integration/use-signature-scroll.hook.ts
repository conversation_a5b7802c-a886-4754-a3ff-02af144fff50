import { useCallback, useEffect, useRef } from "react";
import { ISignaturePosition } from "../../types/signature/signature-overlay.interface";

/**
 * Hook para scroll automático para posição de assinatura
 * Responsabilidade única: gerenciar scroll para assinatura
 * Mantém a lógica de scroll separada do PDF
 */
export const useSignatureScroll = (
	containerRef: React.RefObject<HTMLElement | null>,
	zoom: number
) => {
	const hasScrolledRef = useRef(false);

	/**
	 * Faz scroll para a posição da assinatura
	 */
	const scrollToSignature = useCallback((signature: ISignaturePosition | null): void => {
		if (!signature) return;
		
		const container = containerRef.current;
		if (!container) return;

		const { page, x, y } = signature;
		const canvasSelector = `canvas[data-page="${page + 1}"]`;
		const canvas = container.querySelector(canvasSelector) as HTMLCanvasElement | null;
		if (!canvas) return;

		const targetX = canvas.offsetLeft + x * zoom;
		const targetY = canvas.offsetTop + y * zoom;

		const { width: containerWidth, height: containerHeight } = container.getBoundingClientRect();
		const scrollLeft = targetX - containerWidth / 2;
		const scrollTop = targetY - containerHeight / 2;

		container.scrollTo({
			left: scrollLeft,
			top: scrollTop,
			behavior: "smooth",
		});
	}, [containerRef, zoom]);

	/**
	 * Scroll automático quando assinatura é posicionada
	 */
	const autoScrollToSignature = useCallback((
		signaturePosition: ISignaturePosition | null,
		signatureSvg: string | null,
		forceScroll: boolean = false
	) => {
		if (!signaturePosition || !signatureSvg) {
			hasScrolledRef.current = false;
			return;
		}

		if (hasScrolledRef.current && !forceScroll) return;

		// Aguarda um pouco para garantir que o PDF foi renderizado
		setTimeout(() => {
			scrollToSignature(signaturePosition);
			hasScrolledRef.current = true;
		}, 100);
	}, [scrollToSignature]);

	/**
	 * Reset do estado de scroll
	 */
	const resetScrollState = useCallback(() => {
		hasScrolledRef.current = false;
	}, []);

	return {
		scrollToSignature,
		autoScrollToSignature,
		resetScrollState,
	};
};

/**
 * Hook que combina scroll automático com estados de assinatura
 */
export const useAutoSignatureScroll = (
	containerRef: React.RefObject<HTMLElement | null>,
	zoom: number,
	signaturePosition: ISignaturePosition | null,
	signatureSvg: string | null
) => {
	const { autoScrollToSignature, resetScrollState } = useSignatureScroll(containerRef, zoom);

	// Auto scroll quando assinatura muda
	useEffect(() => {
		autoScrollToSignature(signaturePosition, signatureSvg);
	}, [signaturePosition, signatureSvg, autoScrollToSignature]);

	// Reset quando componente desmonta ou assinatura é removida
	useEffect(() => {
		if (!signaturePosition || !signatureSvg) {
			resetScrollState();
		}
	}, [signaturePosition, signatureSvg, resetScrollState]);

	return {
		resetScrollState,
	};
};
