import { useRef } from "react";
import { usePdfRenderer } from "../hooks/render/render.hook";
import { IPdfOverlay } from "../types/pdf-overlay.interface";
import { PdfCanvasProps } from "../types/pdf-canvas-props.type";

interface PdfCanvasWithOverlaysProps extends PdfCanvasProps {
	overlays?: IPdfOverlay[];
	overlayData?: Record<string, unknown>;
	onRenderOverlays?: (context: CanvasRenderingContext2D, page: number, scale: number, overlayData?: Record<string, unknown>) => Promise<void>;
	onCanvasClick?: (event: React.MouseEvent<HTMLCanvasElement>, pageNumber: number, zoom: number) => void;
}

const PdfCanvas = ({
	pdfDocument,
	pageNumber,
	zoom,
	onRenderComplete,
	forceRenderAllPages,
	overlays,
	overlayData,
	onRenderOverlays,
	onCanvasClick,
}: PdfCanvasWithOverlaysProps) => {
	const canvasRef = useRef<HTMLCanvasElement>(null);

	usePdfRenderer({
		pdfDocument,
		pageNumber,
		zoom,
		canvasRef,
		onRenderComplete,
		overlays,
		overlayData,
		forceRenderAllPages,
		onRenderOverlays,
	});

	const handleClick = (event: React.MouseEvent<HTMLCanvasElement>) => {
		if (onCanvasClick) {
			onCanvasClick(event, pageNumber, zoom);
		}
	};

	return (
		<canvas
			data-page={pageNumber}
			ref={canvasRef}
			style={{ display: "block", margin: "1rem auto", maxWidth: "100%", cursor: "crosshair" }}
			onClick={handleClick}
		/>
	);
};

export default PdfCanvas;
