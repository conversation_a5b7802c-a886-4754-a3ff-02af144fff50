// PdfViewer.tsx
import { use<PERSON>tomValue, useSet<PERSON>tom } from "jotai";
import React, { useEffect, useRef, useState } from "react";
import { usePdfActions } from "../hooks/actions/pdf-acitions.hook";
import { usePdfWithOverlays } from "../hooks/overlay/use-pdf-with-overlays.hook";
import { useLoadedPages } from "../hooks/render/use-loaded-pages.hook";
import { useCurrentVisiblePage } from "../hooks/visual-interactions/current-page-visible.hook";
import { useEndOfPdfObserver } from "../hooks/visual-interactions/end-page-observer.hook";
import { useMaxZoom } from "../hooks/zoom-utils/calculate-max-zoom.hook";
import { useZoomControl } from "../hooks/zoom-utils/pdf-zoom.hook";
import { documentCurrentPage } from "../states/current-page-document.state";
import { isFullPdfLoaded<PERSON>tom } from "../states/is-full-loaded.state";
import { pdfDocumentProxy } from "../states/pdf-proxy.state";
import { IPdfWithOverlaysProps } from "../types/pdf-overlay.interface";
import PdfViewerContent from "./pdf-content";
import PdfViewerHeader from "./pdf-header";

interface PdfViewerProps extends IPdfWithOverlaysProps {
	id: string;
	buffer: ArrayBuffer;
	isModal?: boolean;
	onDownloadRequested?: () => void;
}

const PdfViewer: React.FC<PdfViewerProps> = ({ id, buffer, isModal, overlays, overlayData, onOverlayClick, onDownloadRequested }) => {
	const containerRef = useRef<HTMLDivElement>(null);
	const pdfDocument = useAtomValue(pdfDocumentProxy);
	const totalPages = pdfDocument?.numPages || 1;
	const { loadedPages, handleRenderComplete } = useLoadedPages(id, buffer);
	const currentPage = useAtomValue(documentCurrentPage);
	const { downloadPdf, printPdf } = usePdfActions({ fileName: "document.pdf" });
	const maxZoom = useMaxZoom(pdfDocument, containerRef);
	const { zoom, increaseZoom, decreaseZoom } = useZoomControl({ initialZoom: 1, maxZoom });
	const setIsFullyLoaded = useSetAtom(isFullPdfLoadedAtom);

	// Sistema de overlays
	const { pdfProps } = usePdfWithOverlays({
		overlays,
		overlayData,
		onOverlayClick,
	});

	useCurrentVisiblePage(containerRef);
	useEndOfPdfObserver({ containerRef, totalPages });
	const [forceRenderAllPages] = useState(false);
	const isFullyLoaded = useAtomValue(isFullPdfLoadedAtom);

	useEffect(() => {
		if (loadedPages.size === totalPages) {
			setIsFullyLoaded(true);
		}
	}, [loadedPages, totalPages, setIsFullyLoaded]);

	if (!pdfDocument) return null;
	return (
		<div className="w-full shadow-lg bg-white p-2 rounded-lg">
			<div
				ref={containerRef}
				className="bg-gray-200 rounded-lg"
				style={{
					position: "relative",
					width: "100%",
					overflow: isFullyLoaded ? "auto" : "hidden",
					height: "87vh",
				}}
			>
				{!isModal && (
					<PdfViewerHeader
						currentPage={currentPage}
						totalPages={totalPages}
						zoom={zoom}
						maxZoom={maxZoom}
						onIncreaseZoom={increaseZoom}
						onDecreaseZoom={decreaseZoom}
						onPrint={printPdf}
						onDownload={() => {
							onDownloadRequested?.();
							downloadPdf();
						}}
					/>
				)}
				<PdfViewerContent
					pdfDocument={pdfDocument}
					totalPages={totalPages}
					zoom={zoom}
					onRenderComplete={handleRenderComplete}
					forceRenderAllPages={forceRenderAllPages}
					overlays={pdfProps.overlays}
					overlayData={pdfProps.overlayData}
					onRenderOverlays={pdfProps.onRenderOverlays}
				/>
			</div>
		</div>
	);
};

export default PdfViewer;
