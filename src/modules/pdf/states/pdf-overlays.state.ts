import { atom } from "jotai";
import { IPdfOverlay, IPdfOverlayRenderer } from "../types/pdf-overlay.interface";

/**
 * Atom para armazenar overlays por página
 * Estrutura: Map<pageNumber, IPdfOverlay[]>
 */
export const pdfOverlaysAtom = atom<Map<number, IPdfOverlay[]>>(new Map());

/**
 * Atom para armazenar renderizadores por tipo
 * Estrutura: Map<type, IPdfOverlayRenderer>
 */
export const pdfRenderersAtom = atom<Map<string, IPdfOverlayRenderer>>(new Map());

/**
 * Atom derivado para adicionar overlay
 */
export const addOverlayAtom = atom(
	null,
	(get, set, overlay: IPdfOverlay) => {
		console.log("📝 Adicionando overlay via atom", overlay);
		const currentOverlays = get(pdfOverlaysAtom);
		const newOverlays = new Map(currentOverlays);
		
		const pageOverlays = newOverlays.get(overlay.page) || [];
		const existingIndex = pageOverlays.findIndex(o => o.id === overlay.id);
		
		if (existingIndex >= 0) {
			pageOverlays[existingIndex] = overlay;
		} else {
			pageOverlays.push(overlay);
		}
		
		newOverlays.set(overlay.page, pageOverlays);
		set(pdfOverlaysAtom, newOverlays);
	}
);

/**
 * Atom derivado para remover overlay
 */
export const removeOverlayAtom = atom(
	null,
	(get, set, id: string) => {
		const currentOverlays = get(pdfOverlaysAtom);
		const newOverlays = new Map();
		
		currentOverlays.forEach((pageOverlays, page) => {
			const filteredOverlays = pageOverlays.filter(overlay => overlay.id !== id);
			if (filteredOverlays.length > 0) {
				newOverlays.set(page, filteredOverlays);
			}
		});
		
		set(pdfOverlaysAtom, newOverlays);
	}
);

/**
 * Atom derivado para obter overlays de uma página
 */
export const getOverlaysForPageAtom = atom(
	(get) => (page: number): IPdfOverlay[] => {
		const overlays = get(pdfOverlaysAtom);
		return overlays.get(page) || [];
	}
);

/**
 * Atom derivado para registrar renderizador
 */
export const registerRendererAtom = atom(
	null,
	(get, set, renderer: IPdfOverlayRenderer) => {
		console.log("🔧 Registrando renderizador via atom", renderer.supportedType);
		const currentRenderers = get(pdfRenderersAtom);
		const newRenderers = new Map(currentRenderers);
		newRenderers.set(renderer.supportedType, renderer);
		set(pdfRenderersAtom, newRenderers);
	}
);

/**
 * Atom derivado para obter renderizador
 */
export const getRendererAtom = atom(
	(get) => (type: string): IPdfOverlayRenderer | undefined => {
		const renderers = get(pdfRenderersAtom);
		const renderer = renderers.get(type);
		console.log(`🔍 Buscando renderizador via atom para tipo '${type}':`, renderer);
		console.log("🗂️ Renderizadores disponíveis via atom:", Array.from(renderers.keys()));
		return renderer;
	}
);
