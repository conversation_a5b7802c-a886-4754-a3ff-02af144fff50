import { useCallback, useRef } from "react";

/**
 * Hook para renderização estável de overlays
 * Evita re-renderização desnecessária e piscadas
 */
export const useStableOverlayRendering = () => {
	const lastRenderParamsRef = useRef<{
		zoom: number;
		pageNumber: number;
		overlayCount: number;
	} | null>(null);

	/**
	 * Verifica se os overlays precisam ser re-renderizados
	 */
	const shouldRenderOverlays = useCallback((zoom: number, pageNumber: number, overlayCount: number): boolean => {
		const lastParams = lastRenderParamsRef.current;

		if (!lastParams) {
			return true; // Primeira renderização
		}

		// Re-renderizar apenas se houve mudança significativa
		const zoomChanged = Math.abs(lastParams.zoom - zoom) > 0.01;
		const pageChanged = lastParams.pageNumber !== pageNumber;
		const overlaysChanged = lastParams.overlayCount !== overlayCount;

		return zoomChanged || pageChanged || overlaysChanged;
	}, []);

	/**
	 * Atualiza os parâmetros da última renderização
	 */
	const updateRenderParams = useCallback((zoom: number, pageNumber: number, overlayCount: number) => {
		lastRenderParamsRef.current = {
			zoom,
			pageNumber,
			overlayCount,
		};
	}, []);

	/**
	 * Reseta os parâmetros (força próxima renderização)
	 */
	const resetRenderParams = useCallback(() => {
		lastRenderParamsRef.current = null;
	}, []);

	return {
		shouldRenderOverlays,
		updateRenderParams,
		resetRenderParams,
	};
};
