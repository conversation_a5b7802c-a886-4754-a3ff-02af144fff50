import { useDebounce } from "@/shared/hooks/utils/debounce";
import { PDFDocumentProxy, PDFPageProxy } from "pdfjs-dist";
import { RefObject, useCallback, useEffect, useLayoutEffect, useRef } from "react";
import { IPdfOverlay } from "../../types/pdf-overlay.interface";
import { useVisibility } from "../visual-interactions/use-canva-visible.hook";
import { useStableOverlayRendering } from "./use-stable-overlay-rendering.hook";

export interface IUsePdfRenderer {
	pdfDocument: PDFDocumentProxy;
	pageNumber: number;
	zoom: number;
	canvasRef: React.RefObject<HTMLCanvasElement | null>;
	onRenderComplete?: () => void;
	overlays?: IPdfOverlay[];
	overlayData?: Record<string, unknown>;
	forceRenderAllPages?: boolean;
	onRenderOverlays?: (context: CanvasRenderingContext2D, page: number, scale: number, overlayData?: Record<string, unknown>) => Promise<void>;
}

export const usePdfRenderer = ({
	pdfDocument,
	pageNumber,
	zoom,
	canvasRef,
	onRenderComplete,
	overlays,
	overlayData,
	forceRenderAllPages,
	onRenderOverlays,
}: IUsePdfRenderer) => {
	const renderTaskRef = useRef<ReturnType<PDFPageProxy["render"]> | null>(null);
	const isRenderingOverlaysRef = useRef(false);
	const isVisible = useVisibility(canvasRef as RefObject<HTMLElement>, 0.1);
	const { shouldRenderOverlays, updateRenderParams } = useStableOverlayRendering();

	const cancelRenderTask = () => {
		if (renderTaskRef.current) {
			renderTaskRef.current.cancel();
			renderTaskRef.current = null;
		}
	};

	const renderPage = useCallback(async () => {
		if (!canvasRef.current) return;

		const controller = new AbortController();
		const { signal } = controller;

		try {
			const page = await pdfDocument.getPage(pageNumber);

			if (signal.aborted) return;

			const baseViewport = page.getViewport({ scale: 1 });
			const deviceScale = window.devicePixelRatio || 1;
			const scale = zoom * deviceScale;
			const viewport = page.getViewport({ scale });
			const canvas = canvasRef.current;
			const context = canvas.getContext("2d");
			if (!context) return;

			canvas.width = viewport.width;
			canvas.height = viewport.height;

			if (window.matchMedia("(max-width: 768px)").matches) {
				const containerWidth = canvas.parentElement?.clientWidth || window.innerWidth;
				const aspectRatio = baseViewport.height / baseViewport.width;
				canvas.style.width = "100%";
				canvas.style.height = `${containerWidth * aspectRatio}px`;
			} else {
				canvas.style.width = `${baseViewport.width * zoom}px`;
				canvas.style.height = `${baseViewport.height * zoom}px`;
			}

			if (renderTaskRef.current) renderTaskRef.current.cancel();
			cancelRenderTask();
			const renderTask = page.render({ canvasContext: context, viewport });
			renderTaskRef.current = renderTask;

			await renderTask.promise;

			// Renderiza overlays se disponível e necessário
			const overlayCount = overlays?.length || 0;
			console.log("Contagem de overlay", overlayCount);

			console.log("onRenderOverlays", onRenderOverlays);
			console.log(!isRenderingOverlaysRef.current);
			console.log(shouldRenderOverlays(zoom, pageNumber, overlayCount));
			if (onRenderOverlays && !isRenderingOverlaysRef.current && shouldRenderOverlays(zoom, pageNumber, overlayCount)) {
				console.log("Está qui");
				isRenderingOverlaysRef.current = true;
				try {
					console.log("🎨 Renderizando overlays (estável)", overlayData, "zoom:", zoom, "página:", pageNumber);
					await onRenderOverlays(context, pageNumber - 1, scale, overlayData);
					updateRenderParams(zoom, pageNumber, overlayCount);
				} catch (error) {
					console.error("Erro ao renderizar overlays:", error);
				} finally {
					isRenderingOverlaysRef.current = false;
				}
			}

			if (onRenderComplete) onRenderComplete();
		} catch (error: unknown) {
			if (error instanceof Error) {
				if (error.name === "RenderingCancelledException" || error.message.includes("Rendering cancelled")) return;
			}
			console.error("Erro ao renderizar a página", error);
		}
	}, [
		pdfDocument,
		pageNumber,
		zoom,
		onRenderComplete,
		canvasRef,
		onRenderOverlays,
		overlayData,
		overlays,
		shouldRenderOverlays,
		updateRenderParams,
	]);

	useLayoutEffect(() => {
		if (forceRenderAllPages || (isVisible && canvasRef.current && canvasRef.current.clientWidth > 0 && canvasRef.current.clientHeight > 0)) {
			renderPage();
		}
	}, [forceRenderAllPages, renderPage, canvasRef, isVisible]);

	const debouncedRenderPage = useDebounce(renderPage, 200, [renderPage]);
	useEffect(() => {
		window.addEventListener("resize", debouncedRenderPage);
		return () => {
			window.removeEventListener("resize", debouncedRenderPage);
		};
	}, [debouncedRenderPage]);
};
