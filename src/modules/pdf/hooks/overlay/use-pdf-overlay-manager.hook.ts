// import { use<PERSON><PERSON>, useAtomValue, useSet<PERSON>tom } from "jotai";
// import { useCallback } from "react";
// import { addOverlayAtom, getOverlaysForPageAtom, getRendererAtom, registerRendererAtom, removeOverlayAtom } from "../../states/pdf-overlays.state";
// import { IPdfOverlay, IPdfOverlayRenderer } from "../../types/pdf-overlay.interface";

// /**
//  * Tipo estendido do overlay manager com método getRenderer
//  */
// type ExtendedOverlayManager = {
// 	addOverlay: (overlay: IPdfOverlay) => void;
// 	removeOverlay: (id: string) => void;
// 	getOverlaysForPage: (page: number) => IPdfOverlay[];
// 	registerRenderer: (renderer: IPdfOverlayRenderer) => void;
// 	unregisterRenderer: (type: string) => void;
// 	getRenderer: (type: string) => IPdfOverlayRenderer | undefined;
// };

// /**
//  * Hook para gerenciar overlays no PDF
//  * Implementa o padrão Strategy para renderização extensível
//  * Segue o princípio da responsabilidade única
//  */
// export const usePdfOverlayManager = () => {
// 	const overlaysRef = useRef<Map<number, IPdfOverlay[]>>(new Map());
// 	const renderersRef = useRef<Map<string, IPdfOverlayRenderer>>(new Map());

// 	const addOverlay = useCallback((overlay: IPdfOverlay) => {
// 		console.log("📝 Adicionando overlay", overlay);
// 		const pageOverlays = overlaysRef.current.get(overlay.page) || [];
// 		const existingIndex = pageOverlays.findIndex(o => o.id === overlay.id);

// 		if (existingIndex >= 0) {
// 			pageOverlays[existingIndex] = overlay;
// 		} else {
// 			pageOverlays.push(overlay);
// 		}

// 		overlaysRef.current.set(overlay.page, pageOverlays);
// 	}, []);

// 	const removeOverlay = useCallback((id: string) => {
// 		overlaysRef.current.forEach((pageOverlays, page) => {
// 			const filteredOverlays = pageOverlays.filter(overlay => overlay.id !== id);
// 			if (filteredOverlays.length !== pageOverlays.length) {
// 				overlaysRef.current.set(page, filteredOverlays);
// 			}
// 		});
// 	}, []);

// 	const getOverlaysForPage = useCallback((page: number): IPdfOverlay[] => {
// 		return overlaysRef.current.get(page) || [];
// 	}, []);

// 	const registerRenderer = useCallback((renderer: IPdfOverlayRenderer) => {
// 		console.log("🔧 Registrando renderizador", renderer.supportedType);
// 		renderersRef.current.set(renderer.supportedType, renderer);
// 	}, []);

// 	const unregisterRenderer = useCallback((type: string) => {
// 		renderersRef.current.delete(type);
// 	}, []);

// 	const getRenderer = useCallback((type: string) => {
// 		const renderer = renderersRef.current.get(type);
// 		console.log(`🔍 Buscando renderizador para tipo '${type}':`, renderer);
// 		console.log("🗂️ Renderizadores disponíveis:", Array.from(renderersRef.current.keys()));
// 		return renderer;
// 	}, []);

// 	return {
// 		addOverlay,
// 		removeOverlay,
// 		getOverlaysForPage,
// 		registerRenderer,
// 		unregisterRenderer,
// 		getRenderer, // Expor método para obter renderizador
// 	};
// };

// /**
//  * Hook para renderizar overlays em uma página específica
//  * Responsabilidade única: renderização de overlays
//  */
// export const useRenderOverlays = (overlayManager: ExtendedOverlayManager) => {
// 	const registerRenderer = useCallback(
// 		(renderer: IPdfOverlayRenderer) => {
// 			overlayManager.registerRenderer(renderer);
// 		},
// 		[overlayManager]
// 	);

// 	const renderOverlaysForPage = useCallback(
// 		async (context: CanvasRenderingContext2D, page: number, scale: number, overlayData?: Record<string, unknown>) => {
// 			const overlays = overlayManager.getOverlaysForPage(page);
// 			console.log("🎨 Renderizando overlays para página", page, "com escala", scale, "dados", overlayData);

// 			for (const overlay of overlays) {
// 				console.log("🖼️ Renderizando overlay", overlay);
// 				const renderer = overlayManager.getRenderer(overlay.type);
// 				console.log("🔧 Renderizador encontrado:", renderer);
// 				if (renderer) {
// 					try {
// 						await renderer.render(context, overlay, scale, overlayData?.[overlay.type]);
// 					} catch (error) {
// 						console.error(`❌ Erro ao renderizar overlay ${overlay.id} do tipo ${overlay.type}:`, error);
// 					}
// 				} else {
// 					console.warn(`⚠️ Nenhum renderizador encontrado para tipo: ${overlay.type}`);
// 				}
// 			}
// 		},
// 		[overlayManager]
// 	);

// 	return {
// 		registerRenderer,
// 		renderOverlaysForPage,
// 	};
// };
