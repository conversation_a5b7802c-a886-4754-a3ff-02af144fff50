import { useCallback, useRef } from "react";
import { IPdfOverlay, IPdfOverlayManager, IPdfOverlayRenderer } from "../../types/pdf-overlay.interface";

/**
 * Hook para gerenciar overlays no PDF
 * Implementa o padrão Strategy para renderização extensível
 * Segue o princípio da responsabilidade única
 */
export const usePdfOverlayManager = (): IPdfOverlayManager => {
	const overlaysRef = useRef<Map<number, IPdfOverlay[]>>(new Map());
	const renderersRef = useRef<Map<string, IPdfOverlayRenderer>>(new Map());

	const addOverlay = useCallback((overlay: IPdfOverlay) => {
		const pageOverlays = overlaysRef.current.get(overlay.page) || [];
		const existingIndex = pageOverlays.findIndex(o => o.id === overlay.id);

		if (existingIndex >= 0) {
			pageOverlays[existingIndex] = overlay;
		} else {
			pageOverlays.push(overlay);
		}

		overlaysRef.current.set(overlay.page, pageOverlays);
	}, []);

	const removeOverlay = useCallback((id: string) => {
		overlaysRef.current.forEach((pageOverlays, page) => {
			const filteredOverlays = pageOverlays.filter(overlay => overlay.id !== id);
			if (filteredOverlays.length !== pageOverlays.length) {
				overlaysRef.current.set(page, filteredOverlays);
			}
		});
	}, []);

	const getOverlaysForPage = useCallback((page: number): IPdfOverlay[] => {
		return overlaysRef.current.get(page) || [];
	}, []);

	const registerRenderer = useCallback((renderer: IPdfOverlayRenderer) => {
		renderersRef.current.set(renderer.supportedType, renderer);
	}, []);

	const unregisterRenderer = useCallback((type: string) => {
		renderersRef.current.delete(type);
	}, []);

	return {
		addOverlay,
		removeOverlay,
		getOverlaysForPage,
		registerRenderer,
		unregisterRenderer,
	};
};

/**
 * Hook para renderizar overlays em uma página específica
 * Responsabilidade única: renderização de overlays
 */
export const useRenderOverlays = (overlayManager: IPdfOverlayManager) => {
	const renderersRef = useRef<Map<string, IPdfOverlayRenderer>>(new Map());

	const registerRenderer = useCallback(
		(renderer: IPdfOverlayRenderer) => {
			renderersRef.current.set(renderer.supportedType, renderer);
			overlayManager.registerRenderer(renderer);
		},
		[overlayManager]
	);

	const renderOverlaysForPage = useCallback(
		async (context: CanvasRenderingContext2D, page: number, scale: number, overlayData?: Record<string, unknown>) => {
			const overlays = overlayManager.getOverlaysForPage(page);

			for (const overlay of overlays) {
				const renderer = renderersRef.current.get(overlay.type);
				if (renderer) {
					try {
						await renderer.render(context, overlay, scale, overlayData?.[overlay.type]);
					} catch (error) {
						console.error(`Erro ao renderizar overlay ${overlay.id} do tipo ${overlay.type}:`, error);
					}
				}
			}
		},
		[overlayManager]
	);

	return {
		registerRenderer,
		renderOverlaysForPage,
	};
};
