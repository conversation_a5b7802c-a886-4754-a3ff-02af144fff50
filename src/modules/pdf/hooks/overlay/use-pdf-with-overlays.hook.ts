import { useCallback, useMemo } from "react";
import { IPdfOverlay, IPdfOverlayRenderer, IPdfWithOverlaysProps } from "../../types/pdf-overlay.interface";
import { usePdfOverlayManagerAtoms, useRenderOverlaysAtoms } from "./use-pdf-overlay-manager-atoms.hook";

/**
 * Hook principal para integração de overlays com PDF
 * Responsabilidade única: coordenar overlays no PDF
 * Segue o princípio da inversão de dependência
 */
export const usePdfWithOverlays = (props?: IPdfWithOverlaysProps) => {
	const overlayManager = usePdfOverlayManagerAtoms();
	// console.log("🏗️ usePdfWithOverlays criado com overlayManager (atoms):", overlayManager);
	const { renderOverlaysForPage } = useRenderOverlaysAtoms();

	// Sincroniza overlays externos com o gerenciador interno
	const syncExternalOverlays = useCallback(() => {
		if (props?.overlays) {
			props.overlays.forEach(overlay => {
				console.log("adicionando overlay", overlay);
				overlayManager.addOverlay(overlay);
			});
		}
	}, [props?.overlays, overlayManager]);

	// Função de renderização que será passada para o PDF
	const handleRenderOverlays = useCallback(
		async (context: CanvasRenderingContext2D, page: number, scale: number, overlayData?: Record<string, unknown>) => {
			console.log("Se preparando para renderizar overlays", overlayData);
			// Sincroniza overlays externos antes de renderizar
			syncExternalOverlays();

			// Combina dados de overlay externos com internos
			const combinedOverlayData = {
				...overlayData,
				...props?.overlayData,
			};

			await renderOverlaysForPage(context, page, scale, combinedOverlayData);
		},
		[syncExternalOverlays, renderOverlaysForPage, props?.overlayData]
	);

	// Função para lidar com cliques em overlays
	const handleOverlayClick = useCallback(
		(overlay: IPdfOverlay) => {
			props?.onOverlayClick?.(overlay);
		},
		[props]
	);

	// Props processadas para o componente PDF
	const pdfProps = useMemo(
		() => ({
			overlays: props?.overlays,
			overlayData: props?.overlayData,
			onRenderOverlays: handleRenderOverlays,
		}),
		[props?.overlays, props?.overlayData, handleRenderOverlays]
	);

	// Registro direto no overlayManager
	const directRegisterRenderer = useCallback(
		(renderer: IPdfOverlayRenderer) => {
			console.log("🔧 usePdfWithOverlays.registerRenderer chamado com:", renderer);
			console.log("🔧 Registrando diretamente no overlayManager:", overlayManager);
			overlayManager.registerRenderer(renderer);
		},
		[overlayManager]
	);

	return {
		overlayManager,
		registerRenderer: directRegisterRenderer,
		handleOverlayClick,
		pdfProps,
	};
};
