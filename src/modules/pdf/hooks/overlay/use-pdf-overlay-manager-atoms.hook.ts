import { useAtomValue, useSet<PERSON>tom } from "jotai";
import { useCallback } from "react";
import { addOverlayAtom, getOverlaysForPageAtom, getRendererAtom, registerRendererAtom, removeOverlayAtom } from "../../states/pdf-overlays.state";
// Types são importados nos atoms

/**
 * Hook para gerenciar overlays no PDF usando Jotai atoms
 * Implementa o padrão Strategy para renderização extensível
 * Segue o princípio da responsabilidade única
 *
 * Vantagens dos atoms:
 * - Estado global compartilhado entre componentes
 * - Reatividade automática
 * - Melhor debugging
 * - Evita problemas de referência
 */
export const usePdfOverlayManagerAtoms = () => {
	const addOverlay = useSetAtom(addOverlayAtom);
	const removeOverlay = useSetAtom(removeOverlayAtom);
	const getOverlaysForPage = useAtomValue(getOverlaysForPageAtom);
	const registerRenderer = useSetAtom(registerRendererAtom);
	const getRenderer = useAtomValue(getRendererAtom);

	const unregisterRenderer = useCallback((_type: string) => {
		// TODO: Implementar atom para unregister se necessário
		console.log("⚠️ unregisterRenderer não implementado com atoms ainda");
	}, []);

	return {
		addOverlay,
		removeOverlay,
		getOverlaysForPage,
		registerRenderer,
		unregisterRenderer,
		getRenderer,
	};
};

/**
 * Hook para renderizar overlays usando atoms
 */
export const useRenderOverlaysAtoms = () => {
	const getOverlaysForPage = useAtomValue(getOverlaysForPageAtom);
	const getRenderer = useAtomValue(getRendererAtom);
	const registerRenderer = useSetAtom(registerRendererAtom);

	const renderOverlaysForPage = useCallback(
		async (context: CanvasRenderingContext2D, page: number, scale: number, overlayData?: Record<string, unknown>) => {
			const overlays = getOverlaysForPage(page);
			console.log("🎨 Renderizando overlays via atoms para página", page, "com escala", scale, "dados", overlayData);

			for (const overlay of overlays) {
				console.log("🖼️ Renderizando overlay via atoms", overlay);
				const renderer = getRenderer(overlay.type);
				console.log("🔧 Renderizador encontrado via atoms:", renderer);
				if (renderer) {
					try {
						await renderer.render(context, overlay, scale, overlayData?.[overlay.type]);
					} catch (error) {
						console.error(`❌ Erro ao renderizar overlay ${overlay.id} do tipo ${overlay.type}:`, error);
					}
				} else {
					console.warn(`⚠️ Nenhum renderizador encontrado para tipo: ${overlay.type}`);
				}
			}
		},
		[getOverlaysForPage, getRenderer]
	);

	return {
		registerRenderer,
		renderOverlaysForPage,
	};
};
