# Debug: Problemas com Rúbrica e Scroll

## 🔍 Problemas Identificados

### 1. **Rúbrica não aparece no PDF**
- **Causa**: Possível problema na condição de renderização
- **Solução**: Adicionados logs de debug para verificar estados

### 2. **Scroll automático não funciona**
- **Causa**: Hook de scroll não consegue encontrar o container correto
- **Solução**: Melhorado seletor de container e fallbacks

## 🛠️ Correções Implementadas

### 1. **Sistema de Cliques no PDF**
```typescript
// Adicionado handler de clique no canvas
<canvas onClick={handleCanvasClick} style={{cursor: "crosshair"}} />

// Hook para converter cliques em posições
const handleCanvasClick = (event, pageNumber, zoom) => {
  const position = calculateSignaturePosition(event, pageNumber, zoom);
  setSignaturePosition(position);
};
```

### 2. **Melhorado Sistema de Scroll**
```typescript
// Busca automática do container
const container = document.querySelector('div[style*="overflow: auto"]') || 
                 document.querySelector('div:has(canvas[data-page])') || 
                 document.body;

// Cálculo automático do zoom
const actualZoom = canvasRect.width / canvas.width || zoom;
```

### 3. **Logs de Debug Adicionados**
- ✅ Cliques no PDF: `🖱️ Clique no PDF detectado`
- ✅ Posição calculada: `📍 Posição calculada`
- ✅ Estados da assinatura: `🔍 Debug - signaturePosition/rubricSvg`
- ✅ Criação de overlay: `✅ Criando overlay de assinatura`
- ✅ Scroll automático: `🔄 autoScrollToSignature chamado`

## 🧪 Como Testar

### 1. **Teste de Clique no PDF**
1. Abra o console do navegador (F12)
2. Clique em qualquer lugar do PDF
3. Verifique se aparece: `🖱️ Clique no PDF detectado`
4. Verifique se aparece: `📍 Posição calculada`

### 2. **Teste de Rúbrica**
1. Desenhe uma rúbrica no modal
2. Clique em "Salvar"
3. Verifique no console: `🔍 Debug - rubricSvg: SVG presente`
4. Clique no PDF para posicionar
5. Verifique: `✅ Criando overlay de assinatura`

### 3. **Teste de Scroll**
1. Posicione uma assinatura
2. Verifique no console: `🔄 autoScrollToSignature chamado`
3. Verifique: `📜 Iniciando scroll automático`

## 🔧 Próximos Passos

### Se os logs não aparecem:
1. **Clique não funciona**: Verificar se `onCanvasClick` está sendo passado corretamente
2. **Rúbrica não salva**: Verificar modal de criação de rúbrica
3. **Scroll não funciona**: Verificar seletores de container

### Se os logs aparecem mas não funciona:
1. **Overlay não renderiza**: Verificar `SignatureOverlayRenderer`
2. **Scroll não vai para lugar certo**: Verificar cálculos de posição
3. **Zoom incorreto**: Verificar cálculo de zoom automático

## 📋 Checklist de Verificação

- [ ] Console mostra cliques no PDF
- [ ] Console mostra posição calculada
- [ ] Console mostra SVG presente após desenhar
- [ ] Console mostra criação de overlay
- [ ] Console mostra início de scroll
- [ ] Assinatura aparece no PDF
- [ ] Scroll vai para posição correta

## 🚨 Problemas Conhecidos

1. **Seletor de Container**: Pode não funcionar em todos os layouts
2. **Cálculo de Zoom**: Pode estar incorreto em alguns casos
3. **Timing de Renderização**: Scroll pode acontecer antes da renderização

## 💡 Dicas de Debug

1. **Use o console**: Todos os logs têm emojis para fácil identificação
2. **Teste passo a passo**: Desenhe → Salve → Clique → Verifique
3. **Verifique estados**: Use React DevTools para ver estados dos atoms
4. **Teste em diferentes zooms**: Verifique se funciona com zoom diferente de 1

---

**Nota**: Remova os logs de debug após confirmar que tudo funciona corretamente.
