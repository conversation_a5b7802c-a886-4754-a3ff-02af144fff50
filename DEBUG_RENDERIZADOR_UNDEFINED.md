# Debug: Renderizador Undefined - Tentativa de Correção

## 🔍 Problema Persistente

Mesmo após as correções anteriores, o renderizador ainda chega como `undefined`:
```
🔧 Renderizador encontrado: undefined
```

## 🛠️ Nova Abordagem Implementada

### **Registro Direto no OverlayManager**
Em vez de passar o `registerRenderer` por múltiplas camadas de hooks, agora registramos diretamente no `overlayManager`:

```typescript
// Antes: Múltiplas camadas
usePdfWithOverlays() → useRenderOverlays() → overlayManager.registerRenderer()

// Depois: Registro direto
usePdfWithOverlays() → overlayManager.registerRenderer()
```

### **Logs Detalhados Adicionados**
```typescript
// No usePdfWithOverlays
console.log("🏗️ usePdfWithOverlays criado com overlayManager:", overlayManager);
console.log("🔧 usePdfWithOverlays.registerRenderer chamado com:", renderer);
console.log("🔧 Registrando diretamente no overlayManager:", overlayManager);

// No usePdfOverlayManager
console.log("🔧 Registrando renderizador", renderer.supportedType);
console.log("🔍 Buscando renderizador para tipo 'signature':", renderer);
console.log("🗂️ Renderizadores disponíveis:", Array.from(renderersRef.current.keys()));
```

## 🧪 Logs Esperados Agora

### **Sequência Correta:**
```
🏗️ usePdfWithOverlays criado com overlayManager: [object]
🔧 Criando e registrando renderizador de assinatura
🔧 usePdfWithOverlays.registerRenderer chamado com: SignatureOverlayRenderer {}
🔧 Registrando diretamente no overlayManager: [object]
🔧 Registrando renderizador signature
📍 Definindo posição da assinatura do documento: {pageIndex: 3, coordinates: {x: 145, y: 145}}
✅ Criando overlay de assinatura
📝 Adicionando overlay {id: 'current-signature', type: 'signature', ...}
🎨 Renderizando overlays para página 3
🖼️ Renderizando overlay {id: 'current-signature', type: 'signature', ...}
🔍 Buscando renderizador para tipo 'signature': SignatureOverlayRenderer {}
🗂️ Renderizadores disponíveis: ['signature']
🔧 Renderizador encontrado: SignatureOverlayRenderer {}
🎨 Renderizando assinatura: current-signature
🎉 Assinatura renderizada com sucesso!
```

## 🔍 Diagnóstico

### **Se ainda aparecer `undefined`:**

1. **Verifique se o registro acontece:**
   - Log: `🔧 usePdfWithOverlays.registerRenderer chamado com:`
   - Log: `🔧 Registrando renderizador signature`

2. **Verifique se são instâncias diferentes:**
   - Compare os objetos `overlayManager` nos logs
   - Se forem diferentes, há problema de instância

3. **Verifique renderizadores disponíveis:**
   - Log: `🗂️ Renderizadores disponíveis: ['signature']`
   - Se array estiver vazio, o registro não funcionou

### **Possíveis Causas Restantes:**

1. **Instâncias diferentes de overlayManager**
2. **Timing de execução** (renderização antes do registro)
3. **Problema na referência** do `renderersRef`
4. **Re-renderizações** limpando o estado

## 🎯 Próximos Passos

1. **Execute e verifique os logs** na sequência esperada
2. **Se ainda falhar**, o problema pode estar na arquitetura dos hooks
3. **Considere simplificar** ainda mais, registrando diretamente no componente
4. **Última opção**: Criar um contexto React para compartilhar renderizadores

---

**Teste agora e me informe quais logs aparecem!**
